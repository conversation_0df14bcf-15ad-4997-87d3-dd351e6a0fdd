var resolver = new ApiResolver('objc');
resolver.enumerateMatches('*[NSString stringWithFormat*]',{
    onMatch: function(match){

        Interceptor.attach(match['address'], {
            onEnter: function(args){

            },
            onLeave: function(retval){

                var originalString = ObjC.Object(retval).toString();
            console.log("[+] ---------------------------------------------------------------");
                console.log("stringWithFormat=",ObjC.Object(retval))
                            console.log(Thread.backtrace(this.context, Backtracer.FUZZY)
                .map(addr => {
                    const m = Process.findModuleByAddress(addr);
                    if (m) {
                        const offset = addr.sub(m.base);
                        return `${m.name}!0x${addr.toString(16)} -> IDA偏移: 0x${offset.toString(16)}`;
                    }
                    return `0x${addr.toString(16)}`;
                })
                .join('\n')
            );
            console.log("[+] ---------------------------------------------------------------");
            }
        });


    },
    onComplete: function(){}
});
