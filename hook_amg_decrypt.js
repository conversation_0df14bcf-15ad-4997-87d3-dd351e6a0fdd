// Frida脚本 - Hook AMG应用的URL解密和网络请求
// 目标: 分析 https://m.ejmk123.top/83/rauth.php 的响应数据解密方式

console.log("[+] AMG URL解密分析脚本启动...");

// 工具函数 - 打印十六进制数据
function hexdump(data, length) {
    if (!data) return "null";
    length = length || 256;
    var result = "";
    try {
        var bytes = new Uint8Array(data.bytes ? data.bytes : data);
        var len = Math.min(bytes.length, length);
        for (var i = 0; i < len; i += 16) {
            var hex = "";
            var ascii = "";
            for (var j = 0; j < 16 && i + j < len; j++) {
                var byte = bytes[i + j];
                hex += ("0" + byte.toString(16)).slice(-2) + " ";
                ascii += (byte >= 32 && byte <= 126) ? String.fromCharCode(byte) : ".";
            }
            result += ("0000" + i.toString(16)).slice(-4) + ": " + 
                     hex.padEnd(48) + " |" + ascii + "|\n";
        }
    } catch (e) {
        result = "Error dumping data: " + e.toString();
    }
    return result;
}

// 工具函数 - 解密字符串
function decryptString(encryptedData) {
    try {
        if (!encryptedData) return "null";
        var str = "";
        if (typeof encryptedData === 'string') {
            return encryptedData;
        }
        // 尝试转换为字符串
        var bytes = new Uint8Array(encryptedData);
        for (var i = 0; i < bytes.length; i++) {
            str += String.fromCharCode(bytes[i]);
        }
        return str;
    } catch (e) {
        return "解密失败: " + e.toString();
    }
}

// Hook第一个关键函数 - URL解密和网络请求
// AMG!0x103289004 -> IDA偏移: 0x6c5004 -> 实际地址: 0x1006c4f6c
var func1_addr = Module.findBaseAddress("AMG").add(0x6c4f6c);
console.log("[+] Hook函数1地址: " + func1_addr);

Interceptor.attach(func1_addr, {
    onEnter: function(args) {
        console.log("\n[+] ===== 函数1被调用 (URL解密) =====");
        console.log("[+] 调用栈:");
        console.log(Thread.backtrace(this.context, Backtracer.ACCURATE)
            .map(DebugSymbol.fromAddress).join('\n'));
        
        // 保存上下文
        this.context_saved = this.context;
    },
    
    onLeave: function(retval) {
        console.log("[+] 函数1返回值: " + retval);
        console.log("[+] ===== 函数1调用结束 =====\n");
    }
});

// Hook第二个关键函数 - 请求参数构造和加密
// AMG!0x103279230 -> IDA偏移: 0x6b5230 -> 实际地址: 0x1006b5098  
var func2_addr = Module.findBaseAddress("AMG").add(0x6b5098);
console.log("[+] Hook函数2地址: " + func2_addr);

Interceptor.attach(func2_addr, {
    onEnter: function(args) {
        console.log("\n[+] ===== 函数2被调用 (参数构造) =====");
        console.log("[+] 调用栈:");
        console.log(Thread.backtrace(this.context, Backtracer.ACCURATE)
            .map(DebugSymbol.fromAddress).join('\n'));
    },
    
    onLeave: function(retval) {
        console.log("[+] 函数2返回值: " + retval);
        console.log("[+] ===== 函数2调用结束 =====\n");
    }
});

// Hook NSString stringWithFormat 来捕获解密的字符串
var NSString = ObjC.classes.NSString;
if (NSString) {
    var stringWithFormat = NSString['+ stringWithFormat:'];
    if (stringWithFormat) {
        Interceptor.attach(stringWithFormat.implementation, {
            onEnter: function(args) {
                var format = ObjC.Object(args[2]);
                if (format && format.toString) {
                    var formatStr = format.toString();
                    // 检查是否包含我们关注的加密字符串
                    if (formatStr.includes('\xEB\x95\x40') || formatStr.includes('\xDA\x46d')) {
                        console.log("\n[+] ===== 发现关键字符串格式化 =====");
                        console.log("[+] 格式字符串: " + formatStr);
                        console.log("[+] 格式字符串十六进制:");
                        console.log(hexdump(formatStr));
                        
                        // 打印所有参数
                        for (var i = 3; i < 10; i++) {
                            try {
                                if (args[i] && !args[i].isNull()) {
                                    var arg = ObjC.Object(args[i]);
                                    console.log("[+] 参数" + (i-2) + ": " + arg.toString());
                                }
                            } catch (e) {
                                // 忽略无效参数
                            }
                        }
                        this.isKeyFormat = true;
                    }
                }
            },
            
            onLeave: function(retval) {
                if (this.isKeyFormat && retval && !retval.isNull()) {
                    var result = ObjC.Object(retval);
                    console.log("[+] 格式化结果: " + result.toString());
                    console.log("[+] 结果十六进制:");
                    console.log(hexdump(result.toString()));
                    console.log("[+] ===== 关键字符串格式化结束 =====\n");
                }
            }
        });
    }
}

// Hook NSData dataUsingEncoding 来捕获编码转换
var NSString_dataUsingEncoding = ObjC.classes.NSString['- dataUsingEncoding:'];
if (NSString_dataUsingEncoding) {
    Interceptor.attach(NSString_dataUsingEncoding.implementation, {
        onEnter: function(args) {
            var str = ObjC.Object(args[0]);
            var encoding = args[2];
            if (str && str.toString) {
                var strValue = str.toString();
                // 检查是否是我们关注的字符串
                if (strValue.length > 10) {
                    console.log("\n[+] ===== NSString转NSData =====");
                    console.log("[+] 原字符串: " + strValue);
                    console.log("[+] 编码: " + encoding);
                    this.originalString = strValue;
                }
            }
        },
        
        onLeave: function(retval) {
            if (this.originalString && retval && !retval.isNull()) {
                var data = ObjC.Object(retval);
                console.log("[+] 转换后的NSData长度: " + data.length());
                console.log("[+] NSData内容:");
                console.log(hexdump(data.bytes(), Math.min(data.length(), 256)));
                console.log("[+] ===== NSString转NSData结束 =====\n");
            }
        }
    });
}

// Hook网络请求相关方法
// Hook sendSynchronousRequest
var NSURLConnection = ObjC.classes.NSURLConnection;
if (NSURLConnection) {
    var sendSyncRequest = NSURLConnection['+ sendSynchronousRequest:returningResponse:error:'];
    if (sendSyncRequest) {
        Interceptor.attach(sendSyncRequest.implementation, {
            onEnter: function(args) {
                var request = ObjC.Object(args[2]);
                if (request) {
                    console.log("\n[+] ===== 网络请求发送 =====");
                    console.log("[+] URL: " + request.URL().toString());
                    console.log("[+] HTTP方法: " + request.HTTPMethod().toString());
                    
                    var headers = request.allHTTPHeaderFields();
                    if (headers) {
                        console.log("[+] 请求头: " + headers.toString());
                    }
                    
                    var body = request.HTTPBody();
                    if (body) {
                        console.log("[+] 请求体长度: " + body.length());
                        console.log("[+] 请求体内容:");
                        console.log(hexdump(body.bytes(), Math.min(body.length(), 512)));
                    }
                }
            },
            
            onLeave: function(retval) {
                if (retval && !retval.isNull()) {
                    var responseData = ObjC.Object(retval);
                    console.log("[+] 响应数据长度: " + responseData.length());
                    console.log("[+] 响应数据内容:");
                    console.log(hexdump(responseData.bytes(), Math.min(responseData.length(), 512)));
                    
                    // 尝试解析为字符串
                    try {
                        var responseStr = ObjC.classes.NSString.alloc().initWithData_encoding_(responseData, 4);
                        if (responseStr) {
                            console.log("[+] 响应字符串: " + responseStr.toString());
                        }
                    } catch (e) {
                        console.log("[+] 无法解析为字符串: " + e);
                    }
                    console.log("[+] ===== 网络请求结束 =====\n");
                }
            }
        });
    }
}

// Hook JSON解析相关方法
var NSJSONSerialization = ObjC.classes.NSJSONSerialization;
if (NSJSONSerialization) {
    var jsonObjectWithData = NSJSONSerialization['+ JSONObjectWithData:options:error:'];
    if (jsonObjectWithData) {
        Interceptor.attach(jsonObjectWithData.implementation, {
            onEnter: function(args) {
                var data = ObjC.Object(args[2]);
                if (data) {
                    console.log("\n[+] ===== JSON解析 =====");
                    console.log("[+] JSON数据长度: " + data.length());
                    console.log("[+] JSON原始数据:");
                    console.log(hexdump(data.bytes(), Math.min(data.length(), 512)));

                    // 尝试解析为字符串
                    try {
                        var jsonStr = ObjC.classes.NSString.alloc().initWithData_encoding_(data, 4);
                        if (jsonStr) {
                            console.log("[+] JSON字符串: " + jsonStr.toString());
                        }
                    } catch (e) {
                        console.log("[+] 无法解析JSON为字符串: " + e);
                    }
                }
            },

            onLeave: function(retval) {
                if (retval && !retval.isNull()) {
                    var jsonObj = ObjC.Object(retval);
                    console.log("[+] 解析后的JSON对象: " + jsonObj.toString());
                    console.log("[+] ===== JSON解析结束 =====\n");
                }
            }
        });
    }
}

// Hook混淆的加密类方法
// 尝试找到混淆的类名并hook其方法
setTimeout(function() {
    console.log("[+] 开始搜索混淆的加密类...");

    // 搜索包含大量o和0的类名
    var classes = Object.keys(ObjC.classes);
    for (var i = 0; i < classes.length; i++) {
        var className = classes[i];
        if (className.includes('oo0') && className.length > 50) {
            console.log("[+] 发现可疑加密类: " + className);

            var targetClass = ObjC.classes[className];
            if (targetClass) {
                // Hook init方法
                try {
                    var initMethod = targetClass['- init'];
                    if (initMethod) {
                        Interceptor.attach(initMethod.implementation, {
                            onEnter: function(args) {
                                console.log("\n[+] ===== 加密类初始化 =====");
                                console.log("[+] 类名: " + className);
                            },
                            onLeave: function(retval) {
                                console.log("[+] 加密类初始化完成: " + retval);
                                console.log("[+] ===== 加密类初始化结束 =====\n");
                            }
                        });
                    }
                } catch (e) {
                    console.log("[-] Hook加密类init失败: " + e);
                }

                // 尝试hook其他可能的方法
                var methods = targetClass.$ownMethods;
                for (var j = 0; j < methods.length; j++) {
                    var methodName = methods[j];
                    if (methodName.includes('decrypt') || methodName.includes('encrypt') ||
                        methodName.includes('data') || methodName.includes('string')) {
                        console.log("[+] 发现可疑方法: " + methodName);
                        try {
                            var method = targetClass[methodName];
                            if (method) {
                                Interceptor.attach(method.implementation, {
                                    onEnter: function(args) {
                                        console.log("\n[+] ===== 加密方法调用 =====");
                                        console.log("[+] 方法: " + methodName);
                                        for (var k = 0; k < Math.min(args.length, 5); k++) {
                                            if (args[k] && !args[k].isNull()) {
                                                try {
                                                    var arg = ObjC.Object(args[k]);
                                                    console.log("[+] 参数" + k + ": " + arg.toString());
                                                } catch (e) {
                                                    console.log("[+] 参数" + k + ": " + args[k]);
                                                }
                                            }
                                        }
                                    },
                                    onLeave: function(retval) {
                                        if (retval && !retval.isNull()) {
                                            try {
                                                var result = ObjC.Object(retval);
                                                console.log("[+] 方法返回: " + result.toString());
                                            } catch (e) {
                                                console.log("[+] 方法返回: " + retval);
                                            }
                                        }
                                        console.log("[+] ===== 加密方法调用结束 =====\n");
                                    }
                                });
                            }
                        } catch (e) {
                            console.log("[-] Hook方法失败: " + methodName + " - " + e);
                        }
                    }
                }
            }
        }
    }
}, 1000);

// Hook AES相关的加密函数
setTimeout(function() {
    console.log("[+] 开始Hook AES加密函数...");

    // Hook CommonCrypto的CCCrypt函数
    var CCCrypt = Module.findExportByName("libcommonCrypto.dylib", "CCCrypt");
    if (CCCrypt) {
        Interceptor.attach(CCCrypt, {
            onEnter: function(args) {
                var op = args[0].toInt32();
                var alg = args[1].toInt32();
                var options = args[2].toInt32();
                var keyLength = args[4].toInt32();
                var dataInLength = args[7].toInt32();

                console.log("\n[+] ===== CCCrypt调用 =====");
                console.log("[+] 操作: " + (op == 0 ? "加密" : "解密"));
                console.log("[+] 算法: " + alg + " (0=AES)");
                console.log("[+] 选项: " + options);
                console.log("[+] 密钥长度: " + keyLength);
                console.log("[+] 数据长度: " + dataInLength);

                // 打印密钥
                if (args[3] && !args[3].isNull() && keyLength > 0) {
                    console.log("[+] 密钥:");
                    console.log(hexdump(args[3], keyLength));
                }

                // 打印IV
                if (args[5] && !args[5].isNull()) {
                    console.log("[+] IV:");
                    console.log(hexdump(args[5], 16));
                }

                // 打印输入数据
                if (args[6] && !args[6].isNull() && dataInLength > 0) {
                    console.log("[+] 输入数据:");
                    console.log(hexdump(args[6], Math.min(dataInLength, 256)));
                }
            },

            onLeave: function(retval) {
                console.log("[+] CCCrypt返回: " + retval);
                console.log("[+] ===== CCCrypt调用结束 =====\n");
            }
        });
    }
}, 2000);

console.log("[+] Hook脚本加载完成，等待函数调用...");
