// 只Hook两个关键地址
console.log("[+] Hook AMG关键函数...");

var amgBase = Module.findBaseAddress("AMG");
console.log("[+] AMG基址: " + amgBase);

// Hook第一个函数 - AMG!0x103289004 -> IDA偏移: 0x6c5004 -> 实际函数: 0x6c4f6c
var func1 = amgBase.add(0x6c4f6c);
Interceptor.attach(func1, {
    onEnter: function(args) {
        console.log("\n[+] ===== URL解密函数调用 =====");
        var x19 = this.context.x19;
        console.log("[+] X19寄存器: " + x19);

        // 读取qword_100AD7DB8的值 (用于URL格式化的参数)
        try {
            var paramPtr = amgBase.add(0xAD7DB8).readPointer();
            if (paramPtr && !paramPtr.isNull()) {
                var param = ObjC.Object(paramPtr);
                console.log("[+] URL格式化参数: " + param.toString());
            }
        } catch (e) {
            console.log("[-] 读取URL参数失败: " + e);
        }
    },
    onLeave: function(retval) {
        console.log("[+] 函数1返回: " + retval);

        // 读取解密后的URL字符串
        var x19 = this.context.x19;
        try {
            // 读取存储解密URL的位置
            var urlPtr = x19.add(0x838).readPointer();
            if (urlPtr && !urlPtr.isNull()) {
                var url = ObjC.Object(urlPtr);
                console.log("[+] *** 解密的URL: " + url.toString() + " ***");
            }
        } catch (e) {
            console.log("[-] 读取URL失败: " + e);
        }
        console.log("[+] ===== URL解密函数结束 =====\n");
    }
});

// Hook第二个函数 - AMG!0x103279230 -> IDA偏移: 0x6b5230 -> 实际函数: 0x6b5098
var func2 = amgBase.add(0x6b5098);
Interceptor.attach(func2, {
    onEnter: function(args) {
        console.log("\n[+] ===== 参数构造函数调用 =====");
        var x19 = this.context.x19;
        console.log("[+] X19寄存器: " + x19);

        // 读取函数中使用的加密key: "dh\xF8\x10\xBCZ\xF3\x65\x81\xF7\n\x8F\xB8\xB0\xAF\x06\xDB\x65q\xFA\x8D\xE2\xCC\x54\xD1\xFE:"
        console.log("[+] 尝试读取bundle中的加密配置...");
    },
    onLeave: function(retval) {
        console.log("[+] 函数2返回: " + retval);

        // 在函数返回时读取生成的NSData
        var x19 = this.context.x19;
        try {
            // 读取0x1068偏移处的NSData (dataUsingEncoding的结果)
            var dataPtr = x19.add(0x1068).readPointer();
            if (dataPtr && !dataPtr.isNull()) {
                var data = ObjC.Object(dataPtr);
                console.log("[+] 生成的请求数据长度: " + data.length());
                console.log("[+] 请求数据内容:");
                console.log(hexdump(data.bytes(), data.length()));
            }
        } catch (e) {
            console.log("[-] 读取数据失败: " + e);
        }
        console.log("[+] ===== 参数构造函数结束 =====\n");
    }
});

// Hook stringWithFormat来捕获解密的字符串
Interceptor.attach(ObjC.classes.NSString['+ stringWithFormat:'].implementation, {
    onEnter: function(args) {
        var format = ObjC.Object(args[2]).toString();
        // 检查是否是那两个加密字符串
        if (format.includes('\xEB\x95\x40') || format.includes('\xDA\x46d')) {
            console.log("\n[+] 发现关键字符串解密:");
            console.log("[+] 加密格式: " + JSON.stringify(format));
            this.isTarget = true;
        }
    },
    onLeave: function(retval) {
        if (this.isTarget && retval) {
            var result = ObjC.Object(retval).toString();
            console.log("[+] 解密结果: " + result);
            if (result.includes('ejmk123') || result.includes('http')) {
                console.log("[+] *** 发现目标URL: " + result + " ***");
            }
        }
    }
});

// Hook网络请求来获取完整的请求和响应
Interceptor.attach(ObjC.classes.NSURLConnection['+ sendSynchronousRequest:returningResponse:error:'].implementation, {
    onEnter: function(args) {
        var request = ObjC.Object(args[2]);
        var url = request.URL().toString();
        if (url.includes('ejmk123')) {
            console.log("\n[+] ===== 网络请求到目标服务器 =====");
            console.log("[+] URL: " + url);
            var body = request.HTTPBody();
            if (body) {
                console.log("[+] 请求体 (可能是加密数据):");
                console.log(hexdump(body.bytes(), body.length()));
            }
            this.isTarget = true;
        }
    },
    onLeave: function(retval) {
        if (this.isTarget && retval) {
            var data = ObjC.Object(retval);
            console.log("[+] ===== 服务器响应 (加密数据) =====");
            console.log("[+] 响应长度: " + data.length());
            console.log("[+] 响应内容:");
            console.log(hexdump(data.bytes(), data.length()));

            // 尝试解析为字符串看是否是明文
            try {
                var str = ObjC.classes.NSString.alloc().initWithData_encoding_(data, 4);
                console.log("[+] 响应字符串: " + str.toString());
            } catch (e) {
                console.log("[+] 响应不是明文字符串，可能是加密数据");
            }
        }
    }
});

console.log("[+] Hook完成，等待调用...");