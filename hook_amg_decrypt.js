// 只Hook两个关键地址
console.log("[+] Hook AMG关键函数...");

var amgBase = Module.findBaseAddress("AMG");
console.log("[+] AMG基址: " + amgBase);

// Hook第一个函数 - AMG!0x103289004 -> IDA偏移: 0x6c5004 -> 实际函数: 0x6c4f6c
var func1 = amgBase.add(0x6c4f6c);
Interceptor.attach(func1, {
    onEnter: function(args) {
        console.log("\n[+] ===== URL解密函数调用 =====");
        var x19 = this.context.x19;
        console.log("[+] X19寄存器: " + x19);

        // 读取qword_100AD7DB8的值 (用于URL格式化的参数)
        try {
            var paramPtr = amgBase.add(0xAD7DB8).readPointer();
            if (paramPtr && !paramPtr.isNull()) {
                var param = ObjC.Object(paramPtr);
                console.log("[+] URL格式化参数: " + param.toString());
            }
        } catch (e) {
            console.log("[-] 读取URL参数失败: " + e);
        }
    },
    onLeave: function(retval) {
        console.log("[+] 函数1返回: " + retval);

        // 读取解密后的URL字符串
        var x19 = this.context.x19;
        try {
            // 读取存储解密URL的位置
            var urlPtr = x19.add(0x838).readPointer();
            if (urlPtr && !urlPtr.isNull()) {
                var url = ObjC.Object(urlPtr);
                console.log("[+] *** 解密的URL: " + url.toString() + " ***");
            }
        } catch (e) {
            console.log("[-] 读取URL失败: " + e);
        }
        console.log("[+] ===== URL解密函数结束 =====\n");
    }
});

// Hook第二个函数 - AMG!0x103279230 -> IDA偏移: 0x6b5230 -> 实际函数: 0x6b5098
var func2 = amgBase.add(0x6b5098);
Interceptor.attach(func2, {
    onEnter: function(args) {
        console.log("\n[+] ===== 参数构造函数调用 =====");
        var x19 = this.context.x19;
        console.log("[+] X19寄存器: " + x19);

        // 读取函数中使用的加密key: "dh\xF8\x10\xBCZ\xF3\x65\x81\xF7\n\x8F\xB8\xB0\xAF\x06\xDB\x65q\xFA\x8D\xE2\xCC\x54\xD1\xFE:"
        console.log("[+] 尝试读取bundle中的加密配置...");
    },
    onLeave: function(retval) {
        console.log("[+] 函数2返回: " + retval);

        // 在函数返回时读取生成的NSData
        var x19 = this.context.x19;
        try {
            // 读取0x1068偏移处的NSData (dataUsingEncoding的结果)
            var dataPtr = x19.add(0x1068).readPointer();
            if (dataPtr && !dataPtr.isNull()) {
                var data = ObjC.Object(dataPtr);
                console.log("[+] 生成的请求数据长度: " + data.length());
                console.log("[+] 请求数据内容:");
                console.log(hexdump(data.bytes(), data.length()));
            }
        } catch (e) {
            console.log("[-] 读取数据失败: " + e);
        }
        console.log("[+] ===== 参数构造函数结束 =====\n");
    }
});

// Hook CCCrypt来捕获AES加密的密钥和IV
var CCCrypt = Module.findExportByName("libcommonCrypto.dylib", "CCCrypt");
if (CCCrypt) {
    Interceptor.attach(CCCrypt, {
        onEnter: function(args) {
            var op = args[0].toInt32();
            var alg = args[1].toInt32();
            var options = args[2].toInt32();
            var keyLength = args[4].toInt32();
            var dataInLength = args[7].toInt32();

            console.log("\n[+] ===== CCCrypt调用 (AES加密/解密) =====");
            console.log("[+] 操作: " + (op == 0 ? "加密" : "解密"));
            console.log("[+] 算法: " + alg + " (0=AES)");
            console.log("[+] 选项: " + options);
            console.log("[+] 密钥长度: " + keyLength);
            console.log("[+] 数据长度: " + dataInLength);

            // 打印密钥
            if (args[3] && !args[3].isNull() && keyLength > 0) {
                console.log("[+] *** AES密钥 ***:");
                console.log(hexdump(args[3], keyLength));
            }

            // 打印IV
            if (args[5] && !args[5].isNull()) {
                console.log("[+] *** AES IV ***:");
                console.log(hexdump(args[5], 16));
            }

            // 打印输入数据
            if (args[6] && !args[6].isNull() && dataInLength > 0) {
                console.log("[+] 输入数据:");
                console.log(hexdump(args[6], Math.min(dataInLength, 256)));
            }
        },

        onLeave: function(retval) {
            console.log("[+] CCCrypt返回: " + retval);
            console.log("[+] ===== CCCrypt调用结束 =====\n");
        }
    });
}

console.log("[+] Hook完成，等待调用...");