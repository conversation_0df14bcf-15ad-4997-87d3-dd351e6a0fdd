// 只Hook两个关键地址
console.log("[+] Hook AMG关键函数...");

var amgBase = Module.findBaseAddress("AMG");
console.log("[+] AMG基址: " + amgBase);

// Hook第一个函数 - AMG!0x103289004 -> IDA偏移: 0x6c5004 -> 实际函数: 0x6c4f6c
var func1 = amgBase.add(0x6c4f6c);
Interceptor.attach(func1, {
    onEnter: function(args) {
        console.log("\n[+] ===== URL解密函数调用 =====");
        console.log("[+] X19寄存器: " + this.context.x19);
    },
    onLeave: function(retval) {
        console.log("[+] 函数1返回: " + retval);
        console.log("[+] ===== URL解密函数结束 =====\n");
    }
});

// Hook第二个函数 - AMG!0x103279230 -> IDA偏移: 0x6b5230 -> 实际函数: 0x6b5098
var func2 = amgBase.add(0x6b5098);
Interceptor.attach(func2, {
    onEnter: function(args) {
        console.log("\n[+] ===== 参数构造函数调用 =====");
        console.log("[+] X19寄存器: " + this.context.x19);
    },
    onLeave: function(retval) {
        console.log("[+] 函数2返回: " + retval);
        console.log("[+] ===== 参数构造函数结束 =====\n");
    }
});

console.log("[+] Hook完成，等待调用...");