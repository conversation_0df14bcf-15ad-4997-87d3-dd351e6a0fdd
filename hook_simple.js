// 简化版Frida脚本 - 专门Hook两个关键地址
// AMG!0x103289004 -> IDA偏移: 0x6c5004
// AMG!0x103279230 -> IDA偏移: 0x6b5230

console.log("[+] 开始Hook AMG关键函数...");

// 工具函数
function hexdump(data, length) {
    if (!data) return "null";
    length = length || 128;
    var result = "";
    try {
        var bytes = new Uint8Array(data);
        var len = Math.min(bytes.length, length);
        for (var i = 0; i < len; i += 16) {
            var hex = "";
            var ascii = "";
            for (var j = 0; j < 16 && i + j < len; j++) {
                var byte = bytes[i + j];
                hex += ("0" + byte.toString(16)).slice(-2) + " ";
                ascii += (byte >= 32 && byte <= 126) ? String.fromCharCode(byte) : ".";
            }
            result += ("0000" + i.toString(16)).slice(-4) + ": " + 
                     hex.padEnd(48) + " |" + ascii + "|\n";
        }
    } catch (e) {
        result = "Error: " + e.toString();
    }
    return result;
}

// 获取AMG模块基址
var amgBase = Module.findBaseAddress("AMG");
if (!amgBase) {
    console.log("[-] 找不到AMG模块");
    return;
}
console.log("[+] AMG基址: " + amgBase);

// Hook第一个函数 - URL解密 (0x6c4f6c)
var func1 = amgBase.add(0x6c4f6c);
console.log("[+] Hook函数1: " + func1);

Interceptor.attach(func1, {
    onEnter: function(args) {
        console.log("\n[+] ===== 函数1调用 (URL解密) =====");
        console.log("[+] 时间: " + new Date().toLocaleString());
        
        // 保存寄存器状态
        this.x19 = this.context.x19;
        console.log("[+] X19寄存器: " + this.x19);
    },
    
    onLeave: function(retval) {
        console.log("[+] 函数1返回: " + retval);
        console.log("[+] ===== 函数1结束 =====\n");
    }
});

// Hook第二个函数 - 参数构造 (0x6b5098)
var func2 = amgBase.add(0x6b5098);
console.log("[+] Hook函数2: " + func2);

Interceptor.attach(func2, {
    onEnter: function(args) {
        console.log("\n[+] ===== 函数2调用 (参数构造) =====");
        console.log("[+] 时间: " + new Date().toLocaleString());
        
        // 保存寄存器状态
        this.x19 = this.context.x19;
        console.log("[+] X19寄存器: " + this.x19);
    },
    
    onLeave: function(retval) {
        console.log("[+] 函数2返回: " + retval);
        console.log("[+] ===== 函数2结束 =====\n");
    }
});

// Hook stringWithFormat来捕获解密的字符串
var NSString = ObjC.classes.NSString;
if (NSString) {
    var stringWithFormat = NSString['+ stringWithFormat:'];
    Interceptor.attach(stringWithFormat.implementation, {
        onEnter: function(args) {
            var format = ObjC.Object(args[2]);
            var formatStr = format.toString();
            
            // 检查是否是我们关注的加密字符串
            if (formatStr.includes('\xEB\x95\x40') || formatStr.includes('\xDA\x46d')) {
                console.log("\n[+] ===== 发现关键字符串解密 =====");
                console.log("[+] 加密格式: " + JSON.stringify(formatStr));
                
                // 打印参数
                for (var i = 3; i < 8; i++) {
                    try {
                        if (args[i] && !args[i].isNull()) {
                            var arg = ObjC.Object(args[i]);
                            console.log("[+] 参数" + (i-2) + ": " + arg.toString());
                        }
                    } catch (e) {
                        if (args[i]) {
                            console.log("[+] 参数" + (i-2) + ": " + args[i]);
                        }
                    }
                }
                this.isTarget = true;
            }
        },
        
        onLeave: function(retval) {
            if (this.isTarget && retval && !retval.isNull()) {
                var result = ObjC.Object(retval);
                var resultStr = result.toString();
                console.log("[+] 解密结果: " + resultStr);
                
                // 检查是否是URL
                if (resultStr.includes('http') || resultStr.includes('ejmk123')) {
                    console.log("[+] *** 发现目标URL: " + resultStr + " ***");
                }
                console.log("[+] ===== 字符串解密结束 =====\n");
            }
        }
    });
}

// Hook网络请求
var NSURLConnection = ObjC.classes.NSURLConnection;
if (NSURLConnection) {
    var sendSync = NSURLConnection['+ sendSynchronousRequest:returningResponse:error:'];
    if (sendSync) {
        Interceptor.attach(sendSync.implementation, {
            onEnter: function(args) {
                var request = ObjC.Object(args[2]);
                var url = request.URL().toString();
                
                if (url.includes('ejmk123') || url.includes('rauth')) {
                    console.log("\n[+] ===== 目标网络请求 =====");
                    console.log("[+] URL: " + url);
                    console.log("[+] 方法: " + request.HTTPMethod().toString());
                    
                    var body = request.HTTPBody();
                    if (body) {
                        console.log("[+] 请求体长度: " + body.length());
                        console.log("[+] 请求体:");
                        console.log(hexdump(body.bytes(), body.length()));
                    }
                    this.isTargetRequest = true;
                }
            },
            
            onLeave: function(retval) {
                if (this.isTargetRequest && retval && !retval.isNull()) {
                    var data = ObjC.Object(retval);
                    console.log("[+] 响应长度: " + data.length());
                    console.log("[+] 响应数据:");
                    console.log(hexdump(data.bytes(), Math.min(data.length(), 512)));
                    
                    // 尝试解析为字符串
                    try {
                        var str = ObjC.classes.NSString.alloc().initWithData_encoding_(data, 4);
                        console.log("[+] 响应字符串: " + str.toString());
                    } catch (e) {
                        console.log("[+] 无法解析为字符串");
                    }
                    console.log("[+] ===== 网络请求结束 =====\n");
                }
            }
        });
    }
}

// Hook JSON解析
var NSJSONSerialization = ObjC.classes.NSJSONSerialization;
if (NSJSONSerialization) {
    var jsonParse = NSJSONSerialization['+ JSONObjectWithData:options:error:'];
    if (jsonParse) {
        Interceptor.attach(jsonParse.implementation, {
            onEnter: function(args) {
                var data = ObjC.Object(args[2]);
                console.log("\n[+] ===== JSON解析 =====");
                console.log("[+] 数据长度: " + data.length());
                
                try {
                    var str = ObjC.classes.NSString.alloc().initWithData_encoding_(data, 4);
                    console.log("[+] JSON原文: " + str.toString());
                } catch (e) {
                    console.log("[+] JSON数据:");
                    console.log(hexdump(data.bytes(), Math.min(data.length(), 256)));
                }
            },
            
            onLeave: function(retval) {
                if (retval && !retval.isNull()) {
                    var obj = ObjC.Object(retval);
                    console.log("[+] 解析结果: " + obj.toString());
                }
                console.log("[+] ===== JSON解析结束 =====\n");
            }
        });
    }
}

console.log("[+] Hook设置完成，等待调用...");
